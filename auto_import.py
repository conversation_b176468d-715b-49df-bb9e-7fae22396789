#!/usr/bin/env python3
"""
Automatic Import for Autogen Studio Multi-Agent System
This script automatically imports all agents and workflows without manual intervention
"""

import sqlite3
import json
import os
import time
from datetime import datetime
import uuid

class AutoImporter:
    def __init__(self):
        self.db_path = os.path.expanduser("~/.autogenstudio/autogen04202.db")
        self.agents = []
        self.workflows = []
        
    def connect_db(self):
        """Connect to Autogen Studio database"""
        try:
            conn = sqlite3.connect(self.db_path)
            return conn
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return None
    
    def create_agents_table_if_not_exists(self, conn):
        """Ensure agents table exists with correct schema"""
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS agents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT DEFAULT 'default',
                name TEXT NOT NULL,
                description TEXT,
                system_message TEXT,
                model TEXT DEFAULT 'gpt-4',
                temperature REAL DEFAULT 0.2,
                max_tokens INTEGER DEFAULT 2000,
                tools TEXT DEFAULT '[]',
                created_at TEXT,
                updated_at TEXT
            )
        """)
        conn.commit()
    
    def create_workflows_table_if_not_exists(self, conn):
        """Ensure workflows table exists"""
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS workflows (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT DEFAULT 'default',
                name TEXT NOT NULL,
                description TEXT,
                type TEXT DEFAULT 'group_chat',
                config TEXT,
                created_at TEXT,
                updated_at TEXT
            )
        """)
        conn.commit()
    
    def import_agent(self, conn, agent_config):
        """Import a single agent into the database"""
        cursor = conn.cursor()
        
        # Check if agent already exists
        cursor.execute("SELECT id FROM agents WHERE name = ?", (agent_config['name'],))
        if cursor.fetchone():
            print(f"⚠️ Agent '{agent_config['name']}' already exists, skipping...")
            return None
        
        # Insert agent
        cursor.execute("""
            INSERT INTO agents (user_id, name, description, system_message, model, temperature, max_tokens, tools, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            'default',
            agent_config['name'],
            agent_config['description'],
            agent_config['system_message'],
            agent_config.get('model', 'gpt-4'),
            agent_config.get('temperature', 0.2),
            agent_config.get('max_tokens', 2000),
            json.dumps(agent_config.get('tools', [])),
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))
        
        agent_id = cursor.lastrowid
        conn.commit()
        print(f"✅ Imported agent: {agent_config['name']} (ID: {agent_id})")
        return agent_id
    
    def import_workflow(self, conn, workflow_config, agent_ids):
        """Import workflow into the database"""
        cursor = conn.cursor()
        
        # Check if workflow already exists
        cursor.execute("SELECT id FROM workflows WHERE name = ?", (workflow_config['name'],))
        if cursor.fetchone():
            print(f"⚠️ Workflow '{workflow_config['name']}' already exists, skipping...")
            return None
        
        # Create workflow config
        config = {
            "agents": agent_ids,
            "max_rounds": workflow_config.get('max_rounds', 10),
            "admin_name": workflow_config.get('admin_name', 'planner'),
            "speaker_selection_method": "auto"
        }
        
        cursor.execute("""
            INSERT INTO workflows (user_id, name, description, type, config, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (
            'default',
            workflow_config['name'],
            workflow_config['description'],
            workflow_config.get('type', 'group_chat'),
            json.dumps(config),
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))
        
        workflow_id = cursor.lastrowid
        conn.commit()
        print(f"✅ Imported workflow: {workflow_config['name']} (ID: {workflow_id})")
        return workflow_id
    
    def get_agent_configs(self):
        """Define all agent configurations"""
        return [
            {
                "name": "Project Planner",
                "description": "Analyzes requirements and creates development roadmaps",
                "system_message": "You are a project planner who breaks down complex requirements into actionable tasks. Create clear roadmaps with priorities and dependencies. Always start by understanding the full scope before delegating work.",
                "model": "gpt-4",
                "temperature": 0.1,
                "max_tokens": 2000
            },
            {
                "name": "Frontend Developer",
                "description": "React, TypeScript, and Tailwind CSS expert",
                "system_message": "You are a frontend developer specializing in React, TypeScript, and Tailwind CSS. Create modern, responsive, accessible interfaces with clean component architecture and optimal performance.",
                "model": "gpt-4",
                "temperature": 0.2,
                "max_tokens": 3000
            },
            {
                "name": "Backend Developer",
                "description": "FastAPI and Python backend specialist",
                "system_message": "You are a backend developer expert in FastAPI and Python. Build secure, scalable APIs with proper validation, error handling, and documentation following REST principles.",
                "model": "gpt-4",
                "temperature": 0.2,
                "max_tokens": 3000
            },
            {
                "name": "Database Engineer",
                "description": "PostgreSQL and SQLAlchemy database expert",
                "system_message": "You are a database engineer specializing in PostgreSQL and SQLAlchemy. Design efficient schemas with proper indexing, constraints, and relationships. Create clean migrations.",
                "model": "gpt-4",
                "temperature": 0.1,
                "max_tokens": 2500
            },
            {
                "name": "Auth Expert",
                "description": "JWT authentication and security specialist",
                "system_message": "You are a security expert implementing JWT authentication and RBAC. Ensure secure token management, password hashing, and protection against vulnerabilities.",
                "model": "gpt-4",
                "temperature": 0.1,
                "max_tokens": 2500
            },
            {
                "name": "QA Tester",
                "description": "Pytest and Playwright testing expert",
                "system_message": "You are a QA engineer creating comprehensive test suites with Pytest and Playwright. Focus on high coverage, meaningful test cases, and CI/CD integration.",
                "model": "gpt-4",
                "temperature": 0.2,
                "max_tokens": 3000
            },
            {
                "name": "Code Refiner",
                "description": "Code review and optimization specialist",
                "system_message": "You are a senior code reviewer focusing on performance, maintainability, and best practices. Optimize bottlenecks and ensure clean code principles.",
                "model": "gpt-4",
                "temperature": 0.1,
                "max_tokens": 2500
            },
            {
                "name": "Documentation Writer",
                "description": "Technical documentation and API docs expert",
                "system_message": "You are a technical writer creating comprehensive documentation including OpenAPI specs, README files, and user guides. Make documentation clear and accessible.",
                "model": "gpt-4",
                "temperature": 0.3,
                "max_tokens": 3000
            }
        ]
    
    def get_workflow_config(self):
        """Define workflow configuration"""
        return {
            "name": "Full-Stack Development Team",
            "description": "Complete multi-agent team for building full-stack applications",
            "type": "group_chat",
            "max_rounds": 15,
            "admin_name": "Project Planner"
        }
    
    def run_auto_import(self):
        """Execute the automatic import process"""
        print("🚀 Starting Automatic Import for Autogen Studio...")
        print("="*60)
        
        # Connect to database
        conn = self.connect_db()
        if not conn:
            print("❌ Cannot connect to Autogen Studio database!")
            print("Make sure Autogen Studio is running and has been initialized.")
            return False
        
        try:
            # Ensure tables exist
            self.create_agents_table_if_not_exists(conn)
            self.create_workflows_table_if_not_exists(conn)
            
            # Import agents
            print("\n🤖 Importing agents...")
            agent_configs = self.get_agent_configs()
            agent_ids = []
            
            for agent_config in agent_configs:
                agent_id = self.import_agent(conn, agent_config)
                if agent_id:
                    agent_ids.append(agent_id)
            
            print(f"\n✅ Successfully imported {len(agent_ids)} agents!")
            
            # Import workflow
            if agent_ids:
                print("\n⚡ Creating workflow...")
                workflow_config = self.get_workflow_config()
                workflow_id = self.import_workflow(conn, workflow_config, agent_ids)
                
                if workflow_id:
                    print("✅ Workflow created successfully!")
                else:
                    print("⚠️ Workflow creation failed, but agents are ready!")
            
            conn.close()
            
            # Create success message
            success_msg = f"""
🎉 AUTO-IMPORT COMPLETE!
{"="*60}
✅ {len(agent_ids)} agents imported
✅ 1 workflow created
✅ Ready to use immediately!

🌐 Open Autogen Studio: http://127.0.0.1:8081
🤖 Look for: "Full-Stack Development Team" workflow
📝 Try any prompt like: "Build a task management app with React and FastAPI"

The team will automatically:
- Plan the project
- Design the architecture  
- Build frontend and backend
- Create database schema
- Add authentication
- Write tests
- Generate documentation
- Provide deployment guide

Just start chatting and watch the magic happen! ✨
"""
            
            print(success_msg)
            
            # Save success message to file
            with open('IMPORT_SUCCESS.md', 'w') as f:
                f.write(success_msg)
            
            return True
            
        except Exception as e:
            print(f"❌ Import failed: {e}")
            conn.close()
            return False

def main():
    importer = AutoImporter()
    success = importer.run_auto_import()
    
    if success:
        print("\n🎯 Ready to build anything! Open Autogen Studio and start chatting!")
    else:
        print("\n❌ Import failed. Check the error messages above.")

if __name__ == "__main__":
    main()
