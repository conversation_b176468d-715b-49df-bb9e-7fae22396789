#!/usr/bin/env python3
"""
Test Prompts for Universal Solution Builder
Ready-to-use prompts to test the multi-agent team
"""

test_prompts = [
    {
        "title": "Task Management App",
        "category": "Web Application",
        "complexity": "Medium",
        "prompt": """Build a comprehensive task management web application with the following features:

CORE FEATURES:
- User authentication and registration
- Create, edit, delete, and organize tasks
- Task categories and priority levels
- Due dates and reminders
- Task status tracking (todo, in-progress, completed)
- Search and filter functionality

TECHNICAL REQUIREMENTS:
- Responsive React frontend with TypeScript
- FastAPI backend with Python
- PostgreSQL database
- JWT authentication
- Real-time updates
- Mobile-friendly design with Tailwind CSS

ADDITIONAL FEATURES:
- Team collaboration (assign tasks to team members)
- File attachments for tasks
- Comments and activity history
- Dashboard with analytics
- Export functionality

Please provide complete implementation with testing, documentation, and deployment instructions."""
    },
    {
        "title": "E-commerce Platform",
        "category": "E-commerce",
        "complexity": "High",
        "prompt": """Create a full-featured e-commerce platform with the following specifications:

CUSTOMER FEATURES:
- Product browsing with categories and search
- Shopping cart and wishlist
- User accounts and order history
- Secure checkout with payment processing
- Product reviews and ratings
- Order tracking

ADMIN FEATURES:
- Product management (add, edit, delete)
- Inventory tracking
- Order management
- Customer management
- Sales analytics and reporting
- Discount and coupon system

TECHNICAL STACK:
- React with TypeScript frontend
- FastAPI backend
- PostgreSQL database
- Stripe payment integration
- Image upload and management
- Email notifications

Please include comprehensive testing, API documentation, and production deployment setup."""
    },
    {
        "title": "Social Media Dashboard",
        "category": "Social Platform",
        "complexity": "High",
        "prompt": """Build a social media dashboard application with these features:

USER FEATURES:
- User registration and profile management
- Create, edit, and delete posts
- Like, comment, and share functionality
- Follow/unfollow other users
- Real-time notifications
- Direct messaging system
- Image and video uploads

CONTENT FEATURES:
- News feed with algorithmic sorting
- Hashtag support
- Post categories and filtering
- Search users and content
- Trending topics
- Content moderation tools

TECHNICAL REQUIREMENTS:
- Modern React frontend with TypeScript
- FastAPI backend with WebSocket support
- PostgreSQL with Redis for caching
- JWT authentication with refresh tokens
- Real-time updates using WebSockets
- File upload with cloud storage
- Responsive design

Include comprehensive testing, API documentation, and scalable deployment architecture."""
    },
    {
        "title": "Project Management Tool",
        "category": "Business Tool",
        "complexity": "High",
        "prompt": """Create a comprehensive project management tool with the following capabilities:

PROJECT MANAGEMENT:
- Create and manage multiple projects
- Kanban boards with drag-and-drop
- Gantt charts for timeline visualization
- Task dependencies and milestones
- Resource allocation and workload management
- Time tracking and reporting

TEAM COLLABORATION:
- Team member management and roles
- Real-time collaboration
- File sharing and document management
- Comments and discussions
- Activity feeds and notifications
- Video call integration

REPORTING & ANALYTICS:
- Project progress tracking
- Time and budget reports
- Team performance metrics
- Custom dashboards
- Export functionality
- Automated reporting

TECHNICAL STACK:
- React with TypeScript and modern UI library
- FastAPI backend with WebSocket support
- PostgreSQL database
- Redis for real-time features
- JWT authentication with RBAC
- File storage integration

Provide complete implementation with testing, documentation, and enterprise deployment setup."""
    },
    {
        "title": "Learning Management System",
        "category": "Education",
        "complexity": "High",
        "prompt": """Build a comprehensive Learning Management System (LMS) with these features:

STUDENT FEATURES:
- Course enrollment and progress tracking
- Video lessons with playback controls
- Interactive quizzes and assignments
- Discussion forums
- Grade tracking and certificates
- Mobile-responsive learning interface

INSTRUCTOR FEATURES:
- Course creation and management
- Video upload and streaming
- Quiz and assignment builder
- Student progress monitoring
- Grading and feedback tools
- Analytics and reporting

ADMIN FEATURES:
- User management (students, instructors, admins)
- Course approval and management
- System analytics and reporting
- Payment processing for paid courses
- Content moderation
- System configuration

TECHNICAL REQUIREMENTS:
- React frontend with video player integration
- FastAPI backend with file handling
- PostgreSQL database
- Video streaming capabilities
- JWT authentication with role-based access
- Payment integration (Stripe)
- Email notifications

Include comprehensive testing, API documentation, and scalable deployment for educational institutions."""
    },
    {
        "title": "Inventory Management System",
        "category": "Business System",
        "complexity": "Medium",
        "prompt": """Create a comprehensive inventory management system with the following features:

INVENTORY MANAGEMENT:
- Product catalog with categories and variants
- Stock level tracking and alerts
- Barcode scanning integration
- Multi-location inventory
- Stock movement history
- Automated reorder points

SUPPLIER MANAGEMENT:
- Supplier database and contacts
- Purchase order creation and tracking
- Supplier performance analytics
- Price comparison tools
- Automated ordering workflows

SALES & REPORTING:
- Sales order processing
- Invoice generation
- Inventory valuation reports
- Stock movement reports
- Profit margin analysis
- Low stock alerts and notifications

TECHNICAL STACK:
- React frontend with data visualization
- FastAPI backend
- PostgreSQL database
- Barcode scanning API integration
- PDF generation for reports
- Email notifications
- Export functionality (Excel, CSV)

Please provide complete implementation with testing, documentation, and deployment instructions for small to medium businesses."""
    }
]

def save_test_prompts():
    """Save test prompts to JSON file"""
    with open('UNIVERSAL_TEST_PROMPTS.json', 'w') as f:
        json.dump(test_prompts, f, indent=2)
    
    return test_prompts

def create_usage_guide():
    """Create a comprehensive usage guide"""
    guide = """
# 🎯 Universal Solution Builder - Usage Guide

## 🚀 Quick Start

1. **Open Autogen Studio**: http://127.0.0.1:8081
2. **Find Your Team**: Look for "Universal Solution Builder" or "universal_solution_builder"
3. **Start Building**: Copy any prompt below and start a conversation

## 🧪 Ready-to-Use Test Prompts

Choose any prompt below to test the Universal Solution Builder team:

"""
    
    for i, prompt_data in enumerate(test_prompts, 1):
        guide += f"""
### {i}. {prompt_data['title']} ({prompt_data['complexity']} Complexity)
**Category**: {prompt_data['category']}

```
{prompt_data['prompt']}
```

---
"""
    
    guide += """

## 🎁 What You'll Get for ANY Prompt

The Universal Solution Builder team will automatically provide:

### 📋 Project Planning
- Complete requirements analysis
- Technical architecture design
- Development roadmap and timeline
- Technology stack recommendations

### 💻 Full Implementation
- **Frontend**: Complete React application with TypeScript
- **Backend**: FastAPI server with all endpoints
- **Database**: PostgreSQL schema with migrations
- **Authentication**: JWT-based security system
- **Styling**: Tailwind CSS with responsive design

### 🧪 Quality Assurance
- Unit tests for all components
- Integration tests for APIs
- End-to-end testing with Playwright
- Code quality and performance optimization

### 📚 Complete Documentation
- API documentation (OpenAPI/Swagger)
- Setup and installation guides
- User manuals and tutorials
- Architecture documentation

### 🚀 Deployment Ready
- Docker containerization
- CI/CD pipeline configuration
- Production deployment guides
- Monitoring and logging setup

## 🎮 How to Use

1. **Copy any prompt** from the examples above
2. **Paste it into Autogen Studio** chat with the Universal Solution Builder team
3. **Watch the magic happen** as the 4-agent team collaborates:
   - **Project Planner** analyzes and creates roadmap
   - **Full-Stack Developer** builds complete implementation
   - **Database & DevOps Engineer** handles data and deployment
   - **QA & Documentation Specialist** ensures quality and creates docs

4. **Get your complete solution** ready for production!

## 💡 Pro Tips

- **Be specific** about your requirements for better results
- **Mention your preferred technologies** if you have preferences
- **Ask for specific features** you need
- **Request modifications** if needed - the team can iterate and improve

## 🎯 Ready to Build Anything!

The Universal Solution Builder can handle:
- ✅ Web applications of any complexity
- ✅ E-commerce platforms
- ✅ Social media applications
- ✅ Business management tools
- ✅ Educational platforms
- ✅ Data analytics dashboards
- ✅ Mobile-responsive applications
- ✅ API-first architectures
- ✅ Real-time applications
- ✅ And much more!

Just describe what you want to build, and the team will deliver a complete, production-ready solution! 🚀
"""
    
    with open('USAGE_GUIDE.md', 'w') as f:
        f.write(guide)
    
    return guide

def main():
    print("🧪 Creating Test Prompts and Usage Guide...")
    print("=" * 60)
    
    # Save test prompts
    prompts = save_test_prompts()
    print(f"✅ Created {len(prompts)} test prompts: UNIVERSAL_TEST_PROMPTS.json")
    
    # Create usage guide
    guide = create_usage_guide()
    print("✅ Created usage guide: USAGE_GUIDE.md")
    
    print("\n" + "=" * 60)
    print("🎉 READY TO TEST!")
    print("=" * 60)
    print("🌐 Autogen Studio: http://127.0.0.1:8081")
    print("🤖 Team: Universal Solution Builder")
    print("📖 Guide: USAGE_GUIDE.md")
    print("🧪 Test Prompts: UNIVERSAL_TEST_PROMPTS.json")
    print("\n💡 Copy any test prompt and start building!")

if __name__ == "__main__":
    import json
    main()
