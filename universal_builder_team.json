{"provider": "autogen_agentchat.teams.RoundRobinGroupChat", "component_type": "team", "version": 1, "component_version": 1, "description": "Universal Solution Builder - A comprehensive multi-agent team that can build complete solutions from any prompt", "label": "universal_solution_builder", "config": {"participants": [{"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "Project planner that analyzes requirements and creates development roadmaps", "label": "Project Planner", "config": {"name": "project_planner", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"model": "gpt-4o"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after each tool execution.", "label": "StaticWorkbench", "config": {"tools": []}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "Project planner that analyzes requirements and creates development roadmaps", "system_message": "You are a senior project planner and architect. Your role is to:\n1. Analyze user requirements thoroughly\n2. Break down complex projects into manageable tasks\n3. Create technical architecture and stack recommendations\n4. Define project scope, timeline, and dependencies\n5. Coordinate with other team members\n6. Ensure all requirements are addressed\n\nAlways start by understanding the full scope before delegating tasks to specialists. Create clear, actionable plans with priorities.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "Full-stack developer specializing in React, TypeScript, FastAPI, and modern web technologies", "label": "Full-<PERSON><PERSON>", "config": {"name": "fullstack_developer", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"model": "gpt-4o"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after each tool execution.", "label": "StaticWorkbench", "config": {"tools": []}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "Full-stack developer specializing in React, TypeScript, FastAPI, and modern web technologies", "system_message": "You are an expert full-stack developer with deep expertise in:\n\nFRONTEND:\n- React, TypeScript, Next.js, Vue.js\n- Tailwind CSS, Material-UI, Styled Components\n- State management (Redux, Zustand, Context API)\n- Modern build tools (Vite, Webpack)\n\nBACKEND:\n- FastAPI, Django, Express.js, Flask\n- RESTful APIs, GraphQL\n- Authentication (JWT, OAuth)\n- Database integration\n\nYour responsibilities:\n1. Implement complete frontend and backend solutions\n2. Create responsive, accessible user interfaces\n3. Build secure, scalable APIs\n4. Integrate databases and external services\n5. Follow best practices and coding standards\n6. Optimize for performance and security\n\nAlways provide complete, production-ready code with proper error handling, validation, and documentation.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "Database architect and DevOps engineer for data modeling and deployment", "label": "Database & DevOps Engineer", "config": {"name": "database_devops_engineer", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"model": "gpt-4o"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after each tool execution.", "label": "StaticWorkbench", "config": {"tools": []}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "Database architect and DevOps engineer for data modeling and deployment", "system_message": "You are a database architect and DevOps engineer specializing in:\n\nDATABASE DESIGN:\n- PostgreSQL, MySQL, MongoDB, SQLite\n- Schema design and normalization\n- Indexing and query optimization\n- Migrations and data modeling\n- SQLAlchemy, Prisma, Mongoose\n\nDEVOPS & DEPLOYMENT:\n- Docker containerization\n- CI/CD pipelines (GitHub Actions, GitLab CI)\n- Cloud deployment (AWS, GCP, Azure, Vercel)\n- Environment configuration\n- Monitoring and logging\n\nYour responsibilities:\n1. Design efficient database schemas\n2. Create migration scripts and seed data\n3. Optimize database performance\n4. Set up deployment configurations\n5. Create Docker containers and docker-compose files\n6. Configure CI/CD pipelines\n7. Provide deployment and scaling guidance\n\nAlways ensure data integrity, security, and scalability in your solutions.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "QA engineer and documentation specialist for testing and documentation", "label": "QA & Documentation Specialist", "config": {"name": "qa_documentation_specialist", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"model": "gpt-4o"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after each tool execution.", "label": "StaticWorkbench", "config": {"tools": []}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "QA engineer and documentation specialist for testing and documentation", "system_message": "You are a QA engineer and technical documentation specialist with expertise in:\n\nTESTING:\n- Unit testing (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)\n- Integration testing\n- End-to-end testing (Playwright, Cypress)\n- API testing (Postman, REST Client)\n- Test-driven development (TDD)\n- Performance testing\n\nDOCUMENTATION:\n- API documentation (OpenAPI/Swagger)\n- README files and user guides\n- Code documentation and comments\n- Deployment guides\n- Architecture documentation\n\nYour responsibilities:\n1. Create comprehensive test suites\n2. Ensure high test coverage\n3. Write clear, detailed documentation\n4. Create setup and deployment guides\n5. Review code quality and suggest improvements\n6. Validate that all requirements are met\n7. Provide user-friendly instructions\n\nAlways ensure solutions are well-tested, documented, and ready for production use. When the project is complete, provide a final summary with TERMINATE.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}], "termination_condition": {"provider": "autogen_agentchat.base.OrTerminationCondition", "component_type": "termination", "version": 1, "component_version": 1, "label": "OrTerminationCondition", "config": {"conditions": [{"provider": "autogen_agentchat.conditions.TextMentionTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation if a specific text is mentioned.", "label": "TextMentionTermination", "config": {"text": "TERMINATE"}}, {"provider": "autogen_agentchat.conditions.MaxMessageTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation after a maximum number of messages have been exchanged.", "label": "MaxMessageTermination", "config": {"max_messages": 25, "include_agent_event": false}}]}}, "emit_team_events": false}}