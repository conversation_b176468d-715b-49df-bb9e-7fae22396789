#!/usr/bin/env python3
"""
Automated Autogen Studio Multi-Agent Setup
This script directly creates agents and workflows in Autogen Studio via API/Database
"""

import json
import sqlite3
import os
import requests
import time
from datetime import datetime

class AutogenStudioSetup:
    def __init__(self, db_path=None, api_url="http://127.0.0.1:8081"):
        self.api_url = api_url
        self.db_path = db_path or os.path.expanduser("~/.autogenstudio/autogen04202.db")
        
    def create_agent_via_api(self, agent_config):
        """Create agent via Autogen Studio API"""
        try:
            response = requests.post(f"{self.api_url}/api/agents", json=agent_config)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"API Error: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"API connection failed: {e}")
            return None
    
    def create_agent_via_db(self, agent_config):
        """Create agent directly in SQLite database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Insert agent into database
            cursor.execute("""
                INSERT INTO agents (name, description, system_message, model, temperature, max_tokens, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                agent_config['name'],
                agent_config['description'],
                agent_config['system_message'],
                agent_config.get('model', 'gpt-4'),
                agent_config.get('temperature', 0.2),
                agent_config.get('max_tokens', 2000),
                datetime.now().isoformat(),
                datetime.now().isoformat()
            ))
            
            agent_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return agent_id
            
        except Exception as e:
            print(f"Database error: {e}")
            return None
    
    def setup_comprehensive_agent(self):
        """Create a single comprehensive agent that can handle any prompt"""
        master_agent = {
            "name": "Full-Stack AI Developer",
            "description": "Comprehensive AI agent that can build complete solutions from any prompt",
            "system_message": """You are a Full-Stack AI Developer capable of building complete solutions from any prompt. 

Your capabilities include:
- Project planning and task breakdown
- Frontend development (React, TypeScript, Tailwind CSS, Next.js)
- Backend development (FastAPI, Node.js, Python, Express)
- Database design (PostgreSQL, MongoDB, SQLite)
- Authentication and security (JWT, OAuth, RBAC)
- Testing (Pytest, Jest, Playwright, Cypress)
- DevOps and deployment (Docker, CI/CD)
- Documentation (OpenAPI, README, user guides)

WORKFLOW FOR ANY PROMPT:
1. ANALYZE: Break down the requirements into components
2. PLAN: Create a development roadmap with priorities
3. DESIGN: Plan architecture, database schema, and UI/UX
4. DEVELOP: Build frontend, backend, and database components
5. SECURE: Implement authentication and security measures
6. TEST: Create comprehensive test suites
7. DOCUMENT: Generate complete documentation
8. DEPLOY: Provide deployment instructions

RESPONSE FORMAT:
Always structure your response as:
- **Analysis**: What needs to be built
- **Architecture**: Technical decisions and stack
- **Implementation Plan**: Step-by-step development plan
- **Code**: Complete, production-ready code
- **Testing**: Test cases and validation
- **Documentation**: Setup and usage instructions
- **Deployment**: How to deploy and run

Be comprehensive, provide complete working solutions, and always include error handling, validation, and best practices.""",
            "model": "gpt-4",
            "temperature": 0.2,
            "max_tokens": 4000
        }
        
        # Try API first, then database
        result = self.create_agent_via_api(master_agent)
        if not result:
            result = self.create_agent_via_db(master_agent)
        
        return result
    
    def create_workflow_config(self):
        """Create a workflow configuration for the comprehensive agent"""
        workflow = {
            "name": "Universal Solution Builder",
            "description": "Automated workflow that can build any solution from a prompt",
            "type": "single_agent",
            "agent": "Full-Stack AI Developer",
            "max_iterations": 1,
            "auto_execute": True,
            "prompt_template": """
Build a complete solution for: {user_prompt}

Requirements:
- Provide complete, working code
- Include all necessary files and configurations
- Add comprehensive error handling
- Include testing and validation
- Provide clear documentation
- Make it production-ready

Please follow your standard workflow and provide a complete solution.
"""
        }
        
        with open('universal_workflow.json', 'w') as f:
            json.dump(workflow, f, indent=2)
        
        return workflow

def main():
    print("🚀 Setting up Autogen Studio Multi-Agent System...")
    
    setup = AutogenStudioSetup()
    
    # Create comprehensive agent
    print("Creating Full-Stack AI Developer agent...")
    agent_result = setup.setup_comprehensive_agent()
    
    if agent_result:
        print(f"✅ Agent created successfully: {agent_result}")
    else:
        print("❌ Failed to create agent via API/DB, creating configuration files...")
    
    # Create workflow configuration
    print("Creating universal workflow...")
    workflow = setup.create_workflow_config()
    print(f"✅ Workflow configuration saved: universal_workflow.json")
    
    # Create usage instructions
    instructions = """
# 🎯 Universal Solution Builder - Ready!

## How to Use:

1. **Open Autogen Studio**: http://127.0.0.1:8081

2. **Find Your Agent**: Look for "Full-Stack AI Developer" in the agents list

3. **Start a Conversation**: Use any prompt like:
   - "Build a task management app with React and FastAPI"
   - "Create an e-commerce platform with authentication"
   - "Build a blog system with admin panel"
   - "Create a real-time chat application"

4. **Get Complete Solutions**: The agent will provide:
   - Complete working code
   - Database schemas
   - API endpoints
   - Frontend components
   - Testing suites
   - Documentation
   - Deployment instructions

## Example Prompts:

```
Build a social media dashboard with user authentication, post creation, 
real-time updates, and analytics using React, FastAPI, and PostgreSQL.
```

```
Create a project management tool with team collaboration, task tracking, 
file uploads, and reporting features.
```

```
Build an inventory management system with barcode scanning, stock alerts, 
supplier management, and sales reporting.
```

The agent will automatically handle all aspects of development!
"""
    
    with open('USAGE_INSTRUCTIONS.md', 'w') as f:
        f.write(instructions)
    
    print("✅ Setup complete!")
    print("\n" + "="*60)
    print("🎉 AUTOGEN STUDIO READY FOR ANY PROMPT!")
    print("="*60)
    print(f"🌐 Access: http://127.0.0.1:8081")
    print(f"🤖 Agent: Full-Stack AI Developer")
    print(f"📖 Instructions: USAGE_INSTRUCTIONS.md")
    print("\nJust open the UI and start chatting with any development request!")

if __name__ == "__main__":
    main()
