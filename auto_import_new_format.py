#!/usr/bin/env python3
"""
Auto Import for New Autogen Studio Format
Automatically imports team configurations in the new JSON format
"""

import json
import requests
import os
import time

class NewFormatImporter:
    def __init__(self, base_url="http://127.0.0.1:8081"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_connection(self):
        """Test if Autogen Studio is accessible"""
        try:
            response = self.session.get(f"{self.base_url}")
            return response.status_code == 200
        except:
            return False
    
    def import_team_config(self, config_file):
        """Import team configuration from JSON file"""
        try:
            with open(config_file, 'r') as f:
                team_config = json.load(f)
            
            print(f"📋 Loaded team configuration: {team_config.get('label', 'Unknown')}")
            print(f"📝 Description: {team_config.get('description', 'No description')}")
            
            # Try to import via API (if available)
            try:
                response = self.session.post(f"{self.base_url}/api/teams", json=team_config)
                if response.status_code in [200, 201]:
                    print("✅ Team imported successfully via API!")
                    return True
                else:
                    print(f"⚠️ API import failed: {response.status_code}")
            except Exception as e:
                print(f"⚠️ API import not available: {e}")
            
            # Save for manual import
            output_file = f"IMPORT_{team_config.get('label', 'team')}.json"
            with open(output_file, 'w') as f:
                json.dump(team_config, f, indent=2)
            
            print(f"💾 Configuration saved as: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to import team config: {e}")
            return False
    
    def create_import_instructions(self, team_config):
        """Create detailed import instructions"""
        instructions = f"""
# 🚀 Universal Solution Builder - Import Instructions

## 📋 Team Overview
**Name**: {team_config.get('label', 'Universal Solution Builder')}
**Description**: {team_config.get('description', 'Multi-agent team for building complete solutions')}

## 👥 Team Members ({len(team_config.get('config', {}).get('participants', []))} agents):
"""
        
        participants = team_config.get('config', {}).get('participants', [])
        for i, agent in enumerate(participants, 1):
            agent_config = agent.get('config', {})
            instructions += f"""
### {i}. {agent.get('label', 'Agent')}
- **Name**: {agent_config.get('name', 'N/A')}
- **Role**: {agent.get('description', 'No description')}
- **Model**: {agent_config.get('model_client', {}).get('config', {}).get('model', 'gpt-4o')}
"""
        
        instructions += f"""

## 🔧 How to Import

### Method 1: Direct File Import (Recommended)
1. Open Autogen Studio: http://127.0.0.1:8081
2. Look for "Import" or "Upload" option in the UI
3. Upload the file: `IMPORT_{team_config.get('label', 'team')}.json`
4. The team will be automatically configured

### Method 2: Manual Configuration
If direct import isn't available, create the team manually:

1. **Create New Team** in Autogen Studio
2. **Add {len(participants)} agents** with the configurations shown above
3. **Set team type** to: RoundRobinGroupChat
4. **Configure termination** with max {team_config.get('config', {}).get('termination_condition', {}).get('config', {}).get('conditions', [{}])[1].get('config', {}).get('max_messages', 25)} messages

## 🎯 How to Use

Once imported, you can use this team for ANY development request:

### Example Prompts:
```
Build a task management web app with user authentication, 
real-time updates, and team collaboration using React and FastAPI
```

```
Create an e-commerce platform with product catalog, shopping cart, 
payment processing, and admin dashboard
```

```
Build a social media dashboard with posts, comments, user profiles, 
and real-time notifications
```

```
Create a project management tool with Kanban boards, time tracking, 
file uploads, and reporting features
```

## 🎁 What You'll Get

The team will automatically provide:
- ✅ **Complete Project Analysis** - Requirements breakdown and architecture
- ✅ **Full-Stack Implementation** - Frontend (React/TypeScript) + Backend (FastAPI/Python)
- ✅ **Database Design** - Schema, migrations, and optimization
- ✅ **Authentication System** - JWT, user management, security
- ✅ **Comprehensive Testing** - Unit, integration, and E2E tests
- ✅ **Complete Documentation** - API docs, README, deployment guides
- ✅ **Production Setup** - Docker, CI/CD, deployment instructions

## 🚀 Ready to Build!

The Universal Solution Builder team can handle any development request and deliver complete, production-ready solutions automatically!
"""
        
        with open('IMPORT_INSTRUCTIONS.md', 'w') as f:
            f.write(instructions)
        
        return instructions

def main():
    print("🚀 Auto Import for New Autogen Studio Format")
    print("=" * 60)
    
    importer = NewFormatImporter()
    
    # Test connection
    print("🔗 Testing connection to Autogen Studio...")
    if not importer.test_connection():
        print("❌ Cannot connect to Autogen Studio!")
        print("Make sure it's running at http://127.0.0.1:8081")
        return
    
    print("✅ Connected to Autogen Studio!")
    
    # Import the universal builder team
    config_file = "universal_builder_team.json"
    print(f"\n📥 Importing team configuration from {config_file}...")
    
    success = importer.import_team_config(config_file)
    
    if success:
        # Load config for instructions
        with open(config_file, 'r') as f:
            team_config = json.load(f)
        
        # Create import instructions
        print("\n📖 Creating import instructions...")
        instructions = importer.create_import_instructions(team_config)
        print("✅ Instructions created: IMPORT_INSTRUCTIONS.md")
        
        print("\n" + "=" * 60)
        print("🎉 IMPORT READY!")
        print("=" * 60)
        print("🌐 Autogen Studio: http://127.0.0.1:8081")
        print("📁 Import File: IMPORT_universal_solution_builder.json")
        print("📖 Instructions: IMPORT_INSTRUCTIONS.md")
        print("\n💡 Follow the instructions to complete the import!")
        print("Once imported, you can build ANY solution with a single prompt! 🚀")
        
    else:
        print("❌ Import preparation failed!")

if __name__ == "__main__":
    main()
