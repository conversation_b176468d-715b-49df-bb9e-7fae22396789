[{"title": "Photo Gallery Website", "category": "Photography", "complexity": "Medium", "prompt": "Build a professional photo gallery website with the following features:\n\nCORE FEATURES:\n- Image upload with drag-and-drop functionality\n- Responsive masonry grid layout\n- Lightbox modal for full-size viewing\n- Image categories and tags\n- Search and filter functionality\n- User authentication and personal galleries\n\nTECHNICAL REQUIREMENTS:\n- React frontend with TypeScript\n- FastAPI backend\n- PostgreSQL database\n- Cloud storage integration (AWS S3 or Cloudinary)\n- Automatic image optimization and thumbnail generation\n- Mobile-responsive design with Tailwind CSS\n\nADDITIONAL FEATURES:\n- EXIF data extraction and display\n- Social sharing capabilities\n- Image download options\n- Admin panel for content management\n- Performance optimization for fast loading\n\nPlease provide complete implementation with testing, documentation, and deployment instructions."}, {"title": "Art Portfolio Platform", "category": "Creative", "complexity": "High", "prompt": "Create a comprehensive art portfolio platform for artists with these specifications:\n\nARTIST FEATURES:\n- Portfolio creation and customization\n- Artwork upload with detailed metadata\n- Gallery organization and curation\n- Artist profile and biography\n- Commission request system\n- Sales and pricing management\n\nVIEWER FEATURES:\n- Browse artists and artworks\n- Advanced search and filtering\n- Favorite artworks and artists\n- Commission inquiry system\n- Social features (comments, likes, shares)\n- Purchase and payment processing\n\nTECHNICAL STACK:\n- Modern React frontend with animations\n- FastAPI backend with image processing\n- PostgreSQL with full-text search\n- Redis for caching\n- Stripe payment integration\n- CDN for fast image delivery\n\nInclude comprehensive testing, API documentation, and scalable deployment setup."}, {"title": "Corporate Media Library", "category": "Business", "complexity": "High", "prompt": "Build an enterprise media library system for corporate asset management:\n\nMEDIA MANAGEMENT:\n- Bulk image and video upload\n- Automated tagging and categorization\n- Version control for media assets\n- Usage rights and licensing tracking\n- Brand compliance checking\n- Asset approval workflows\n\nUSER MANAGEMENT:\n- Role-based access control\n- Department-specific galleries\n- Usage analytics and reporting\n- Download tracking and permissions\n- Collaboration tools and comments\n- Integration with design tools\n\nTECHNICAL REQUIREMENTS:\n- Enterprise-grade React application\n- FastAPI with advanced authentication\n- PostgreSQL with audit logging\n- Elasticsearch for advanced search\n- Docker containerization\n- Enterprise security standards\n\nProvide complete implementation with enterprise deployment, monitoring, and maintenance documentation."}, {"title": "Social Photo Sharing App", "category": "Social Media", "complexity": "High", "prompt": "Create a social photo sharing application with community features:\n\nSOCIAL FEATURES:\n- User profiles and photo streams\n- Follow/unfollow functionality\n- Photo likes, comments, and shares\n- Real-time notifications\n- Direct messaging with photo sharing\n- Photo contests and challenges\n\nPHOTO FEATURES:\n- Instagram-style filters and editing\n- Story-style temporary posts\n- Photo albums and collections\n- Geotagging and location sharing\n- Privacy controls for photos\n- Automatic face detection and tagging\n\nTECHNICAL STACK:\n- React with real-time updates\n- FastAPI with WebSocket support\n- PostgreSQL with Redis caching\n- Image processing pipeline\n- Push notifications\n- Mobile-responsive PWA design\n\nInclude comprehensive testing, real-time features, and scalable social media architecture."}, {"title": "Real Estate Photo Gallery", "category": "Real Estate", "complexity": "Medium", "prompt": "Build a real estate photo gallery system for property listings:\n\nPROPERTY FEATURES:\n- Property photo galleries with room categorization\n- Virtual tour integration\n- Before/after renovation galleries\n- Floor plan image management\n- 360-degree photo support\n- Property comparison galleries\n\nREAL ESTATE TOOLS:\n- MLS integration for property data\n- Agent profile and contact forms\n- Property search with photo previews\n- Favorite properties and saved searches\n- Photo watermarking with agent branding\n- Lead generation from photo views\n\nTECHNICAL REQUIREMENTS:\n- React frontend with map integration\n- FastAPI backend with MLS APIs\n- PostgreSQL with geospatial support\n- Image optimization for web and mobile\n- SEO optimization for property pages\n- Analytics for photo engagement\n\nProvide complete implementation with real estate-specific features and deployment guidance."}, {"title": "Event Photo Management System", "category": "Events", "complexity": "Medium", "prompt": "Create an event photo management system for photographers and event organizers:\n\nEVENT FEATURES:\n- Event creation and photo gallery setup\n- Bulk photo upload from multiple photographers\n- Automatic event date and location tagging\n- Guest photo sharing and downloads\n- Photo selection and approval workflows\n- Event highlight reels and slideshows\n\nPHOTOGRAPHER TOOLS:\n- Multi-photographer collaboration\n- Client delivery and proofing galleries\n- Watermarking and copyright protection\n- Print ordering integration\n- Photo licensing and usage rights\n- Client communication tools\n\nTECHNICAL STACK:\n- React with drag-and-drop interfaces\n- FastAPI with background processing\n- PostgreSQL with event scheduling\n- Cloud storage with CDN delivery\n- Email notifications and sharing\n- Mobile-optimized viewing experience\n\nInclude comprehensive testing, photographer workflow optimization, and event management features."}]