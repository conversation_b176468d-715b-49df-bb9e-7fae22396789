#!/usr/bin/env python3
"""
Verify Autogen Studio Setup and Create Test Interface
"""

import sqlite3
import os
import json

def verify_import():
    """Verify that agents and workflows were imported correctly"""
    db_path = os.path.expanduser("~/.autogenstudio/autogen04202.db")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check agents
        cursor.execute("SELECT id, name, description FROM agents")
        agents = cursor.fetchall()
        
        print("🤖 IMPORTED AGENTS:")
        print("-" * 50)
        for agent_id, name, description in agents:
            print(f"ID: {agent_id} | {name}")
            print(f"   {description}")
            print()
        
        # Check workflows
        cursor.execute("SELECT id, name, description, config FROM workflows")
        workflows = cursor.fetchall()
        
        print("⚡ IMPORTED WORKFLOWS:")
        print("-" * 50)
        for workflow_id, name, description, config in workflows:
            print(f"ID: {workflow_id} | {name}")
            print(f"   {description}")
            if config:
                config_data = json.loads(config)
                print(f"   Agents: {len(config_data.get('agents', []))}")
                print(f"   Max Rounds: {config_data.get('max_rounds', 'N/A')}")
            print()
        
        conn.close()
        
        return len(agents), len(workflows)
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return 0, 0

def create_test_prompts():
    """Create sample test prompts for the team"""
    test_prompts = [
        {
            "title": "Task Management App",
            "prompt": "Build a task management web application with user authentication, task creation/editing, categories, due dates, and a responsive dashboard using React and FastAPI with PostgreSQL."
        },
        {
            "title": "E-commerce Platform",
            "prompt": "Create an e-commerce platform with product catalog, shopping cart, payment integration, user accounts, admin panel, and order management using modern web technologies."
        },
        {
            "title": "Blog System",
            "prompt": "Build a blog platform with user registration, post creation with rich text editor, comments, categories, search functionality, and admin moderation features."
        },
        {
            "title": "Real-time Chat App",
            "prompt": "Create a real-time chat application with user authentication, multiple chat rooms, private messaging, file sharing, and online status indicators."
        },
        {
            "title": "Project Management Tool",
            "prompt": "Build a project management system with team collaboration, task assignment, progress tracking, file uploads, time tracking, and reporting dashboard."
        },
        {
            "title": "Inventory Management",
            "prompt": "Create an inventory management system with product tracking, stock alerts, supplier management, barcode scanning, sales reporting, and multi-location support."
        }
    ]
    
    with open('TEST_PROMPTS.json', 'w') as f:
        json.dump(test_prompts, f, indent=2)
    
    return test_prompts

def main():
    print("🔍 VERIFYING AUTOGEN STUDIO SETUP")
    print("=" * 60)
    
    # Verify import
    agent_count, workflow_count = verify_import()
    
    if agent_count > 0 and workflow_count > 0:
        print("✅ VERIFICATION SUCCESSFUL!")
        print(f"   • {agent_count} agents imported")
        print(f"   • {workflow_count} workflows created")
        
        # Create test prompts
        print("\n📝 Creating test prompts...")
        test_prompts = create_test_prompts()
        print(f"✅ Created {len(test_prompts)} test prompts")
        
        # Create usage instructions
        instructions = f"""
# 🎯 AUTOGEN STUDIO READY!

## ✅ Setup Status
- **Agents**: {agent_count} imported successfully
- **Workflows**: {workflow_count} created successfully
- **Status**: Ready to use immediately!

## 🚀 How to Use

1. **Open Autogen Studio**: http://127.0.0.1:8081

2. **Find Your Team**: Look for "Full-Stack Development Team" in workflows

3. **Start Building**: Use any of these test prompts or create your own:

### 🧪 Test Prompts:
"""
        
        for i, prompt in enumerate(test_prompts, 1):
            instructions += f"\n**{i}. {prompt['title']}**\n```\n{prompt['prompt']}\n```\n"
        
        instructions += """
## 🎁 What You'll Get

For any prompt, the team will automatically provide:
- ✅ Complete project analysis and planning
- ✅ Full-stack architecture design
- ✅ Frontend code (React + TypeScript + Tailwind)
- ✅ Backend APIs (FastAPI + Python)
- ✅ Database schema and migrations
- ✅ Authentication and security implementation
- ✅ Comprehensive testing suite
- ✅ Complete documentation
- ✅ Deployment instructions

## 🎮 Start Building!

Just open the UI, select the workflow, and paste any prompt. 
The team will collaborate automatically to build your complete solution!

**No manual configuration needed - everything is automated!** 🚀
"""
        
        with open('READY_TO_USE.md', 'w') as f:
            f.write(instructions)
        
        print("\n" + "=" * 60)
        print("🎉 EVERYTHING IS READY!")
        print("=" * 60)
        print("🌐 Autogen Studio: http://127.0.0.1:8081")
        print("🤖 Team: Full-Stack Development Team")
        print("📖 Instructions: READY_TO_USE.md")
        print("🧪 Test Prompts: TEST_PROMPTS.json")
        print("\n💡 Just open the UI and start building anything you want!")
        
    else:
        print("❌ VERIFICATION FAILED!")
        print("   • Run auto_import.py again to fix the setup")

if __name__ == "__main__":
    main()
