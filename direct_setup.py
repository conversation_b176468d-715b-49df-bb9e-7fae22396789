#!/usr/bin/env python3
"""
Direct Autogen Studio Setup via HTTP API
Creates agents and workflows directly through Autogen Studio's REST API
"""

import requests
import json
import time
import os

class DirectAutogenSetup:
    def __init__(self, base_url="http://127.0.0.1:8081"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_connection(self):
        """Test if Autogen Studio is accessible"""
        try:
            response = self.session.get(f"{self.base_url}/api/version")
            return response.status_code == 200
        except:
            return False
    
    def create_master_agent(self):
        """Create the master agent that can handle any prompt"""
        agent_data = {
            "type": "assistant",
            "config": {
                "name": "Universal Solution Builder",
                "description": "AI agent that can build complete solutions from any prompt - handles full-stack development, databases, testing, and deployment",
                "system_message": """You are the Universal Solution Builder - an expert AI developer capable of creating complete, production-ready solutions from any prompt.

CORE CAPABILITIES:
• Full-stack web development (React, Next.js, Vue, Angular)
• Backend APIs (FastAPI, Express, Django, Flask)
• Databases (PostgreSQL, MongoDB, MySQL, SQLite)
• Authentication & Security (JWT, OAuth, RBAC)
• Testing (Jest, Pytest, Playwright, Cypress)
• DevOps (Docker, CI/CD, deployment)
• Mobile development (React Native, Flutter)
• Desktop apps (Electron, Tauri)

WORKFLOW FOR ANY REQUEST:
1. 🔍 ANALYZE: Break down requirements into technical components
2. 🏗️ ARCHITECT: Design system architecture and tech stack
3. 📋 PLAN: Create step-by-step implementation roadmap
4. 💻 CODE: Generate complete, working code with best practices
5. 🧪 TEST: Create comprehensive test suites
6. 📚 DOCUMENT: Provide setup instructions and documentation
7. 🚀 DEPLOY: Include deployment and production guidelines

OUTPUT STRUCTURE:
Always provide:
• **Project Analysis** - Requirements breakdown
• **Technical Architecture** - Stack decisions and system design
• **Complete Implementation** - All code files with proper structure
• **Database Schema** - If applicable, with migrations
• **API Documentation** - Endpoints and usage examples
• **Testing Suite** - Unit, integration, and E2E tests
• **Setup Instructions** - Step-by-step deployment guide
• **Production Checklist** - Security, performance, monitoring

QUALITY STANDARDS:
✅ Production-ready code with error handling
✅ Security best practices and validation
✅ Responsive design and accessibility
✅ Comprehensive testing coverage
✅ Clear documentation and comments
✅ Scalable and maintainable architecture
✅ Performance optimization
✅ Cross-platform compatibility

You can handle ANY development request - from simple utilities to complex enterprise applications. Always deliver complete, working solutions that can be deployed immediately.""",
                "llm_config": {
                    "model": "gpt-4",
                    "temperature": 0.2,
                    "max_tokens": 4000,
                    "api_key": os.getenv('OPENAI_API_KEY')
                },
                "human_input_mode": "NEVER",
                "code_execution_config": False
            }
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/agents", json=agent_data)
            if response.status_code in [200, 201]:
                print("✅ Master agent created successfully!")
                return response.json()
            else:
                print(f"❌ Failed to create agent: {response.status_code}")
                print(f"Response: {response.text}")
                return None
        except Exception as e:
            print(f"❌ Error creating agent: {e}")
            return None
    
    def create_workflow(self, agent_id):
        """Create a workflow using the master agent"""
        workflow_data = {
            "name": "Universal Solution Builder Workflow",
            "description": "Automated workflow that builds complete solutions from any prompt",
            "type": "sequential",
            "agents": [agent_id],
            "max_rounds": 1,
            "admin_name": "Universal Solution Builder"
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/workflows", json=workflow_data)
            if response.status_code in [200, 201]:
                print("✅ Workflow created successfully!")
                return response.json()
            else:
                print(f"❌ Failed to create workflow: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ Error creating workflow: {e}")
            return None

def main():
    print("🚀 Setting up Universal Solution Builder in Autogen Studio...")
    print("="*60)
    
    setup = DirectAutogenSetup()
    
    # Test connection
    print("🔗 Testing connection to Autogen Studio...")
    if not setup.test_connection():
        print("❌ Cannot connect to Autogen Studio!")
        print("Make sure it's running at http://127.0.0.1:8081")
        return
    
    print("✅ Connected to Autogen Studio!")
    
    # Create master agent
    print("\n🤖 Creating Universal Solution Builder agent...")
    agent = setup.create_master_agent()
    
    if agent:
        agent_id = agent.get('id')
        print(f"✅ Agent created with ID: {agent_id}")
        
        # Create workflow
        print("\n⚡ Creating automated workflow...")
        workflow = setup.create_workflow(agent_id)
        
        if workflow:
            print("✅ Workflow created successfully!")
        else:
            print("⚠️ Workflow creation failed, but agent is ready to use")
    
    # Create usage guide
    usage_guide = """
# 🎯 Universal Solution Builder - Ready to Use!

## 🌐 Access Autogen Studio
Open: http://127.0.0.1:8081

## 🤖 Your Agent
Look for: **"Universal Solution Builder"**

## 🚀 How to Use
Just start a conversation with ANY development request:

### Example Prompts:

**Web Applications:**
```
Build a task management app with user authentication, real-time updates, 
and team collaboration features using React and FastAPI.
```

**E-commerce:**
```
Create an online store with product catalog, shopping cart, payment 
processing, and admin dashboard.
```

**Data Applications:**
```
Build a data analytics dashboard with file upload, data visualization, 
and export features using Python and React.
```

**Mobile Apps:**
```
Create a React Native app for expense tracking with offline support 
and cloud synchronization.
```

**Enterprise Tools:**
```
Build a CRM system with lead management, sales pipeline, reporting, 
and email integration.
```

## 🎁 What You Get
For ANY prompt, you'll receive:
- ✅ Complete working code
- ✅ Database schemas and migrations
- ✅ API documentation
- ✅ Frontend components
- ✅ Authentication system
- ✅ Testing suites
- ✅ Deployment instructions
- ✅ Production-ready configuration

## 🎮 Start Building!
Open Autogen Studio and start chatting with your Universal Solution Builder!
"""
    
    with open('READY_TO_USE.md', 'w') as f:
        f.write(usage_guide)
    
    print("\n" + "="*60)
    print("🎉 SETUP COMPLETE!")
    print("="*60)
    print("🌐 Autogen Studio: http://127.0.0.1:8081")
    print("🤖 Agent: Universal Solution Builder")
    print("📖 Guide: READY_TO_USE.md")
    print("\n💡 Just open the UI and ask for ANY development project!")
    print("The agent will build complete solutions automatically! 🚀")

if __name__ == "__main__":
    main()
