[{"title": "Task Management App", "category": "Web Application", "complexity": "Medium", "prompt": "Build a comprehensive task management web application with the following features:\n\nCORE FEATURES:\n- User authentication and registration\n- Create, edit, delete, and organize tasks\n- Task categories and priority levels\n- Due dates and reminders\n- Task status tracking (todo, in-progress, completed)\n- Search and filter functionality\n\nTECHNICAL REQUIREMENTS:\n- Responsive React frontend with TypeScript\n- FastAPI backend with Python\n- PostgreSQL database\n- JWT authentication\n- Real-time updates\n- Mobile-friendly design with Tailwind CSS\n\nADDITIONAL FEATURES:\n- Team collaboration (assign tasks to team members)\n- File attachments for tasks\n- Comments and activity history\n- Dashboard with analytics\n- Export functionality\n\nPlease provide complete implementation with testing, documentation, and deployment instructions."}, {"title": "E-commerce Platform", "category": "E-commerce", "complexity": "High", "prompt": "Create a full-featured e-commerce platform with the following specifications:\n\nCUSTOMER FEATURES:\n- Product browsing with categories and search\n- Shopping cart and wishlist\n- User accounts and order history\n- Secure checkout with payment processing\n- Product reviews and ratings\n- Order tracking\n\nADMIN FEATURES:\n- Product management (add, edit, delete)\n- Inventory tracking\n- Order management\n- Customer management\n- Sales analytics and reporting\n- Discount and coupon system\n\nTECHNICAL STACK:\n- React with TypeScript frontend\n- FastAPI backend\n- PostgreSQL database\n- Stripe payment integration\n- Image upload and management\n- Email notifications\n\nPlease include comprehensive testing, API documentation, and production deployment setup."}, {"title": "Social Media Dashboard", "category": "Social Platform", "complexity": "High", "prompt": "Build a social media dashboard application with these features:\n\nUSER FEATURES:\n- User registration and profile management\n- Create, edit, and delete posts\n- Like, comment, and share functionality\n- Follow/unfollow other users\n- Real-time notifications\n- Direct messaging system\n- Image and video uploads\n\nCONTENT FEATURES:\n- News feed with algorithmic sorting\n- Hashtag support\n- Post categories and filtering\n- Search users and content\n- Trending topics\n- Content moderation tools\n\nTECHNICAL REQUIREMENTS:\n- Modern React frontend with TypeScript\n- FastAPI backend with WebSocket support\n- PostgreSQL with Redis for caching\n- JWT authentication with refresh tokens\n- Real-time updates using WebSockets\n- File upload with cloud storage\n- Responsive design\n\nInclude comprehensive testing, API documentation, and scalable deployment architecture."}, {"title": "Project Management Tool", "category": "Business Tool", "complexity": "High", "prompt": "Create a comprehensive project management tool with the following capabilities:\n\nPROJECT MANAGEMENT:\n- Create and manage multiple projects\n- Kanban boards with drag-and-drop\n- Gantt charts for timeline visualization\n- Task dependencies and milestones\n- Resource allocation and workload management\n- Time tracking and reporting\n\nTEAM COLLABORATION:\n- Team member management and roles\n- Real-time collaboration\n- File sharing and document management\n- Comments and discussions\n- Activity feeds and notifications\n- Video call integration\n\nREPORTING & ANALYTICS:\n- Project progress tracking\n- Time and budget reports\n- Team performance metrics\n- Custom dashboards\n- Export functionality\n- Automated reporting\n\nTECHNICAL STACK:\n- React with TypeScript and modern UI library\n- FastAPI backend with WebSocket support\n- PostgreSQL database\n- Redis for real-time features\n- JWT authentication with RBAC\n- File storage integration\n\nProvide complete implementation with testing, documentation, and enterprise deployment setup."}, {"title": "Learning Management System", "category": "Education", "complexity": "High", "prompt": "Build a comprehensive Learning Management System (LMS) with these features:\n\nSTUDENT FEATURES:\n- Course enrollment and progress tracking\n- Video lessons with playback controls\n- Interactive quizzes and assignments\n- Discussion forums\n- Grade tracking and certificates\n- Mobile-responsive learning interface\n\nINSTRUCTOR FEATURES:\n- Course creation and management\n- Video upload and streaming\n- Quiz and assignment builder\n- Student progress monitoring\n- Grading and feedback tools\n- Analytics and reporting\n\nADMIN FEATURES:\n- User management (students, instructors, admins)\n- Course approval and management\n- System analytics and reporting\n- Payment processing for paid courses\n- Content moderation\n- System configuration\n\nTECHNICAL REQUIREMENTS:\n- React frontend with video player integration\n- FastAPI backend with file handling\n- PostgreSQL database\n- Video streaming capabilities\n- JWT authentication with role-based access\n- Payment integration (Stripe)\n- Email notifications\n\nInclude comprehensive testing, API documentation, and scalable deployment for educational institutions."}, {"title": "Inventory Management System", "category": "Business System", "complexity": "Medium", "prompt": "Create a comprehensive inventory management system with the following features:\n\nINVENTORY MANAGEMENT:\n- Product catalog with categories and variants\n- Stock level tracking and alerts\n- Barcode scanning integration\n- Multi-location inventory\n- Stock movement history\n- Automated reorder points\n\nSUPPLIER MANAGEMENT:\n- Supplier database and contacts\n- Purchase order creation and tracking\n- Supplier performance analytics\n- Price comparison tools\n- Automated ordering workflows\n\nSALES & REPORTING:\n- Sales order processing\n- Invoice generation\n- Inventory valuation reports\n- Stock movement reports\n- Profit margin analysis\n- Low stock alerts and notifications\n\nTECHNICAL STACK:\n- React frontend with data visualization\n- FastAPI backend\n- PostgreSQL database\n- Barcode scanning API integration\n- PDF generation for reports\n- Email notifications\n- Export functionality (Excel, CSV)\n\nPlease provide complete implementation with testing, documentation, and deployment instructions for small to medium businesses."}]