[{"title": "Task Management App", "prompt": "Build a task management web application with user authentication, task creation/editing, categories, due dates, and a responsive dashboard using React and FastAPI with PostgreSQL."}, {"title": "E-commerce Platform", "prompt": "Create an e-commerce platform with product catalog, shopping cart, payment integration, user accounts, admin panel, and order management using modern web technologies."}, {"title": "Blog System", "prompt": "Build a blog platform with user registration, post creation with rich text editor, comments, categories, search functionality, and admin moderation features."}, {"title": "Real-time Chat App", "prompt": "Create a real-time chat application with user authentication, multiple chat rooms, private messaging, file sharing, and online status indicators."}, {"title": "Project Management Tool", "prompt": "Build a project management system with team collaboration, task assignment, progress tracking, file uploads, time tracking, and reporting dashboard."}, {"title": "Inventory Management", "prompt": "Create an inventory management system with product tracking, stock alerts, supplier management, barcode scanning, sales reporting, and multi-location support."}]