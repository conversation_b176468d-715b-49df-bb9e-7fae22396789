# 🧪 Test Gallery Prompt - Ready to Use!

## 📸 Quick Test Prompt

Co<PERSON> and paste this into Autogen Studio to test your Gallery Project Development Team:

```
Build a professional photo gallery website with the following features:

CORE FEATURES:
- Drag-and-drop image upload with progress indicators
- Responsive masonry grid layout for photo display
- Lightbox modal for full-size image viewing
- Image categories and tagging system
- Search and filter functionality
- User authentication and personal galleries

TECHNICAL REQUIREMENTS:
- React frontend with TypeScript
- FastAPI backend with Python
- PostgreSQL database for metadata
- Cloud storage integration (AWS S3 or Cloudinary)
- Automatic image optimization and thumbnail generation
- Mobile-responsive design with Tailwind CSS

ADDITIONAL FEATURES:
- EXIF data extraction and display
- Social sharing capabilities
- Image download options with watermarking
- Admin panel for content management
- Performance optimization for fast loading
- SEO optimization for gallery pages

Please provide complete implementation with:
- Full source code for frontend and backend
- Database schema and migrations
- Image processing pipeline
- Deployment configuration with Docker
- Comprehensive testing suite
- API documentation
- User guide and setup instructions
```

## 🎯 Expected Results

The 5-developer Gallery Project Development Team will collaborate to provide:

### 🏗️ Gallery Architect Will:
- Analyze requirements and create project roadmap
- Design system architecture and user experience
- Plan image optimization and performance strategies
- Coordinate team tasks and ensure quality

### 🎨 Frontend Gallery Developer Will:
- Create responsive React components
- Build drag-and-drop upload interface
- Implement masonry grid and lightbox
- Add mobile-responsive design with Tailwind

### ⚙️ Backend Media Developer Will:
- Build FastAPI endpoints for image management
- Implement image processing and optimization
- Set up cloud storage integration
- Create secure upload validation

### 🗄️ Database Media Engineer Will:
- Design PostgreSQL schema for media metadata
- Create efficient indexing for search
- Build tagging and categorization system
- Optimize queries for performance

### 🚀 DevOps & QA Specialist Will:
- Create comprehensive testing suite
- Set up Docker deployment configuration
- Configure CDN for image delivery
- Provide final documentation and summary

## 🚀 Ready to Test!

1. Open Autogen Studio: http://127.0.0.1:8081
2. Find "Gallery Project Development Team"
3. Paste the test prompt above
4. Watch the 5 developers collaborate automatically!

The team will deliver a complete, production-ready photo gallery application! 🖼️✨
