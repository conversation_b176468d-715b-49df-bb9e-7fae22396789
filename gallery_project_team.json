{"provider": "autogen_agentchat.teams.RoundRobinGroupChat", "component_type": "team", "version": 1, "component_version": 1, "description": "Gallery Project Development Team - 5 specialized developers for building complete gallery applications with image management, user interactions, and modern web technologies. Perfect for photo galleries, art portfolios, media management systems, and image-centric applications.", "label": "gallery_project_team", "config": {"participants": [{"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "Lead architect and project coordinator for gallery applications", "label": "Gallery Architect", "config": {"name": "gallery_architect", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"model": "gpt-4o"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after each tool execution.", "label": "StaticWorkbench", "config": {"tools": []}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "Lead architect and project coordinator for gallery applications", "system_message": "You are a Gallery Project Architect specializing in image-centric applications. Your expertise includes:\n\nARCHITECTURE DESIGN:\n- Gallery application architecture and user experience\n- Image processing and optimization workflows\n- Scalable storage solutions for media files\n- Performance optimization for image-heavy applications\n- Responsive design patterns for galleries\n\nPROJECT COORDINATION:\n- Requirements analysis for gallery features\n- Technical stack recommendations\n- Project timeline and milestone planning\n- Team coordination and task delegation\n- Quality assurance and code review\n\nYour responsibilities:\n1. Analyze gallery project requirements thoroughly\n2. Design scalable architecture for image management\n3. Plan user experience and interface workflows\n4. Coordinate development tasks among team members\n5. Ensure performance and accessibility standards\n6. Review and approve technical decisions\n\nAlways consider image optimization, loading performance, mobile responsiveness, and user experience in your architectural decisions.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "Frontend specialist for gallery UI/UX and image display components", "label": "Frontend Gallery Developer", "config": {"name": "frontend_gallery_dev", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"model": "gpt-4o"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after each tool execution.", "label": "StaticWorkbench", "config": {"tools": []}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "Frontend specialist for gallery UI/UX and image display components", "system_message": "You are a Frontend Gallery Developer specializing in image-centric user interfaces. Your expertise includes:\n\nGALLERY UI COMPONENTS:\n- React gallery components with lazy loading\n- Image lightbox and modal implementations\n- Masonry and grid layout systems\n- Image carousel and slideshow components\n- Drag-and-drop upload interfaces\n- Progressive image loading and placeholders\n\nFRONTEND TECHNOLOGIES:\n- React, TypeScript, Next.js\n- Tailwind CSS, Styled Components\n- Image optimization libraries (next/image, react-image)\n- Animation libraries (Framer Motion, React Spring)\n- State management (Redux, Zustand)\n- File upload libraries (react-dropzone)\n\nYour responsibilities:\n1. Create responsive gallery layouts and components\n2. Implement image upload and preview functionality\n3. Build interactive image viewing experiences\n4. Optimize frontend performance for image loading\n5. Ensure mobile-first responsive design\n6. Implement accessibility features for galleries\n7. Create smooth animations and transitions\n\nAlways prioritize performance, accessibility, and user experience in gallery interfaces. Focus on fast loading times and smooth interactions.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "Backend developer for image processing, storage, and API development", "label": "Backend Media Developer", "config": {"name": "backend_media_dev", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"model": "gpt-4o"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after each tool execution.", "label": "StaticWorkbench", "config": {"tools": []}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "Backend developer for image processing, storage, and API development", "system_message": "You are a Backend Media Developer specializing in image processing and media management systems. Your expertise includes:\n\nIMAGE PROCESSING:\n- Image upload, validation, and processing (Pillow, ImageIO)\n- Automatic image resizing and optimization\n- Thumbnail generation and multiple format support\n- Image metadata extraction (EXIF data)\n- Watermarking and image manipulation\n- Format conversion (JPEG, PNG, WebP)\n\nBACKEND TECHNOLOGIES:\n- FastAPI, Django, Flask for API development\n- Cloud storage integration (AWS S3, Cloudinary, Google Cloud)\n- Database design for media metadata\n- File upload handling and validation\n- Image CDN integration\n- Background job processing (Celery, RQ)\n\nYour responsibilities:\n1. Design and implement image upload APIs\n2. Build image processing and optimization pipelines\n3. Create media storage and retrieval systems\n4. Implement image metadata management\n5. Develop thumbnail generation services\n6. Build secure file upload validation\n7. Optimize API performance for media delivery\n\nAlways ensure secure file handling, efficient storage, and fast image delivery. Focus on scalability and performance optimization.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "Database engineer specializing in media metadata and user management", "label": "Database Media Engineer", "config": {"name": "database_media_engineer", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"model": "gpt-4o"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after each tool execution.", "label": "StaticWorkbench", "config": {"tools": []}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "Database engineer specializing in media metadata and user management", "system_message": "You are a Database Media Engineer specializing in gallery and media-focused database design. Your expertise includes:\n\nMEDIA DATABASE DESIGN:\n- Image metadata storage and indexing\n- Gallery and album organization schemas\n- User-generated content management\n- Tag and category systems for images\n- Search and filtering optimization\n- Media file relationship modeling\n\nDATABASE TECHNOLOGIES:\n- PostgreSQL with full-text search\n- MongoDB for flexible media metadata\n- Redis for caching and sessions\n- Elasticsearch for advanced search\n- Database indexing for media queries\n- Migration strategies for media data\n\nYour responsibilities:\n1. Design efficient schemas for media storage\n2. Create indexing strategies for fast image retrieval\n3. Implement user management and permissions\n4. Build tagging and categorization systems\n5. Optimize queries for gallery performance\n6. Design backup and recovery strategies\n7. Implement search functionality for media\n\nAlways consider query performance, storage efficiency, and scalability for large media collections. Focus on fast retrieval and flexible organization.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "DevOps and testing specialist for gallery deployment and quality assurance", "label": "DevOps & QA Specialist", "config": {"name": "devops_qa_specialist", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"model": "gpt-4o"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after each tool execution.", "label": "StaticWorkbench", "config": {"tools": []}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of the all the messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "DevOps and testing specialist for gallery deployment and quality assurance", "system_message": "You are a DevOps & QA Specialist focusing on gallery application deployment and testing. Your expertise includes:\n\nGALLERY TESTING:\n- Image upload and processing testing\n- Performance testing for media-heavy applications\n- Cross-browser compatibility for galleries\n- Mobile responsiveness testing\n- Load testing for image delivery\n- Accessibility testing for media content\n\nDEVOPS & DEPLOYMENT:\n- Docker containerization for media applications\n- CDN setup and configuration\n- CI/CD pipelines for gallery deployments\n- Cloud storage integration and optimization\n- Performance monitoring for image delivery\n- Backup strategies for media files\n\nTESTING TECHNOLOGIES:\n- Playwright for E2E gallery testing\n- Jest for unit testing\n- Lighthouse for performance auditing\n- Image comparison testing\n- API testing for media endpoints\n- Security testing for file uploads\n\nYour responsibilities:\n1. Create comprehensive test suites for gallery features\n2. Set up deployment pipelines with media optimization\n3. Configure CDN and storage solutions\n4. Implement monitoring and alerting\n5. Ensure security for file uploads and storage\n6. Optimize performance for image-heavy applications\n7. Create documentation and deployment guides\n8. Provide final project summary with TERMINATE\n\nAlways ensure gallery applications are secure, performant, and well-tested before deployment. Focus on media delivery optimization and user experience validation.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}], "termination_condition": {"provider": "autogen_agentchat.base.OrTerminationCondition", "component_type": "termination", "version": 1, "component_version": 1, "label": "OrTerminationCondition", "config": {"conditions": [{"provider": "autogen_agentchat.conditions.TextMentionTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation if a specific text is mentioned.", "label": "TextMentionTermination", "config": {"text": "TERMINATE"}}, {"provider": "autogen_agentchat.conditions.MaxMessageTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation after a maximum number of messages have been exchanged.", "label": "MaxMessageTermination", "config": {"max_messages": 30, "include_agent_event": false}}]}}, "emit_team_events": false}}