{"agents": [{"name": "planner", "role": "Decomposes the prompt into tasks, assigns them to appropriate agents."}, {"name": "frontend_dev", "role": "Creates a responsive React frontend using Tailwind CSS and TypeScript."}, {"name": "backend_dev", "role": "Implements RESTful APIs in FastAPI with proper validation and routing."}, {"name": "db_engineer", "role": "Designs PostgreSQL-compatible schema using SQLAlchemy and sets up Alembic migrations."}, {"name": "auth_expert", "role": "Implements JWT authentication and access control in the backend."}, {"name": "tester", "role": "Creates <PERSON><PERSON><PERSON> for backend and Play<PERSON> for frontend to ensure test coverage."}, {"name": "refiner", "role": "Reviews and refactors the codebase for best practices and performance."}, {"name": "doc_writer", "role": "Writes OpenAPI documentation and README for the project."}]}