#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to set up agents in Autogen Studio
This script helps create the agent configurations for import into Autogen Studio
"""

import json
import os
from pathlib import Path

def create_autogen_studio_configs():
    """Create individual agent configuration files for Autogen Studio"""
    
    # Load the agents configuration
    with open('agents_config.json', 'r') as f:
        agents_data = json.load(f)
    
    # Create configs directory
    configs_dir = Path('autogen_configs')
    configs_dir.mkdir(exist_ok=True)
    
    # Create individual agent files
    for agent in agents_data['agents']:
        agent_config = {
            "type": "assistant",
            "config": {
                "name": agent['name'],
                "description": agent['description'],
                "system_message": agent['system_message'],
                "llm_config": {
                    "model": agent['model'],
                    "temperature": agent['temperature'],
                    "max_tokens": agent['max_tokens'],
                    "api_key": os.getenv('OPENAI_API_KEY')
                },
                "human_input_mode": "NEVER",
                "code_execution_config": False
            }
        }
        
        # Save individual agent config
        agent_file = configs_dir / f"{agent['id']}_config.json"
        with open(agent_file, 'w') as f:
            json.dump(agent_config, f, indent=2)
        
        print(f"Created config for {agent['name']}: {agent_file}")
    
    # Create a team configuration file
    team_config = {
        "name": "Full-Stack Development Team",
        "description": "Complete team for building full-stack web applications",
        "agents": [agent['id'] for agent in agents_data['agents']],
        "workflow_type": "group_chat",
        "max_round": 10,
        "admin_name": "planner"
    }
    
    team_file = configs_dir / "team_config.json"
    with open(team_file, 'w') as f:
        json.dump(team_config, f, indent=2)
    
    print(f"Created team configuration: {team_file}")
    
    return configs_dir

def print_import_instructions():
    """Print instructions for importing into Autogen Studio"""
    print("\n" + "="*60)
    print("AUTOGEN STUDIO IMPORT INSTRUCTIONS")
    print("="*60)
    print("\n1. Open Autogen Studio UI at: http://127.0.0.1:8081")
    print("\n2. Navigate to the 'Agents' section")
    print("\n3. For each agent, click 'Create New Agent' and use these configurations:")
    
    with open('agents_config.json', 'r') as f:
        agents_data = json.load(f)
    
    for i, agent in enumerate(agents_data['agents'], 1):
        print(f"\n   Agent {i}: {agent['name']}")
        print(f"   - Name: {agent['name']}")
        print(f"   - Description: {agent['description']}")
        print(f"   - System Message: {agent['system_message'][:100]}...")
        print(f"   - Model: {agent['model']}")
        print(f"   - Temperature: {agent['temperature']}")
    
    print("\n4. Create a new 'Team' or 'Workflow':")
    print("   - Add all the agents you created")
    print("   - Set the workflow type to 'Group Chat'")
    print("   - Set 'planner' as the admin/coordinator")
    print("   - Set max rounds to 10")
    
    print("\n5. Test the team with a sample project request")
    print("\nConfiguration files have been created in the 'autogen_configs' directory")
    print("You can reference these when manually setting up agents in the UI.")

if __name__ == "__main__":
    print("Setting up Autogen Studio agent configurations...")
    configs_dir = create_autogen_studio_configs()
    print_import_instructions()
